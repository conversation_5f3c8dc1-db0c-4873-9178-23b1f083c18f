"""
Loan Prediction Predictive Analytics Module
==========================================

This module implements various machine learning algorithms for loan prediction,
including model training, evaluation, and comparison.

Author: Business Intelligence Team
Date: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from xgboost import XGBClassifier
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, 
                           roc_auc_score, confusion_matrix, classification_report, roc_curve)
from sklearn.preprocessing import StandardScaler
from imblearn.over_sampling import SMOTE
import joblib
import warnings
warnings.filterwarnings('ignore')

class LoanPredictionModels:
    """
    A comprehensive machine learning class for loan prediction with multiple algorithms.
    """
    
    def __init__(self, data):
        """
        Initialize the model class with processed data.
        
        Args:
            data (pd.DataFrame): Processed dataset
        """
        self.data = data
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.model_results = {}
        self.scaler = StandardScaler()
        
    def prepare_features(self, target_column='Loan_Status_Encoded'):
        """
        Prepare features and target variables for machine learning.
        
        Args:
            target_column (str): Name of the target column
        """
        print("Preparing features for machine learning...")
        
        # Select numerical and encoded categorical features
        feature_columns = []
        
        # Add numerical columns
        numerical_cols = self.data.select_dtypes(include=[np.number]).columns.tolist()
        
        # Remove target and ID columns
        exclude_cols = [target_column, 'Loan_Status_Encoded', 'Loan_Status']
        if 'Loan_ID' in numerical_cols:
            exclude_cols.append('Loan_ID')
        
        feature_columns = [col for col in numerical_cols if col not in exclude_cols]
        
        # Add encoded categorical columns
        encoded_cols = [col for col in self.data.columns if col.endswith('_Encoded') and col != target_column]
        feature_columns.extend(encoded_cols)
        
        print(f"Selected features: {feature_columns}")
        
        # Prepare X and y
        self.X = self.data[feature_columns].fillna(0)  # Handle any remaining NaN values
        self.y = self.data[target_column]
        
        print(f"Feature matrix shape: {self.X.shape}")
        print(f"Target variable shape: {self.y.shape}")
        print(f"Target distribution:\n{self.y.value_counts()}")
        
    def split_data(self, test_size=0.2, random_state=42):
        """
        Split data into training and testing sets.
        
        Args:
            test_size (float): Proportion of test data
            random_state (int): Random state for reproducibility
        """
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X, self.y, test_size=test_size, random_state=random_state, stratify=self.y
        )
        
        print(f"Training set shape: {self.X_train.shape}")
        print(f"Test set shape: {self.X_test.shape}")
        
        # Scale features
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
    def handle_class_imbalance(self, method='smote'):
        """
        Handle class imbalance using SMOTE or other techniques.
        
        Args:
            method (str): Method to handle imbalance ('smote', 'none')
        """
        if method == 'smote':
            print("Applying SMOTE for class balancing...")
            smote = SMOTE(random_state=42)
            self.X_train_balanced, self.y_train_balanced = smote.fit_resample(self.X_train_scaled, self.y_train)
            print(f"Balanced training set shape: {self.X_train_balanced.shape}")
            print(f"Balanced target distribution:\n{pd.Series(self.y_train_balanced).value_counts()}")
        else:
            self.X_train_balanced = self.X_train_scaled
            self.y_train_balanced = self.y_train
    
    def initialize_models(self):
        """
        Initialize various machine learning models with optimized parameters.
        """
        self.models = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'XGBoost': XGBClassifier(random_state=42, eval_metric='logloss'),
            'SVM': SVC(probability=True, random_state=42),
            'Naive Bayes': GaussianNB(),
            'K-Nearest Neighbors': KNeighborsClassifier(n_neighbors=5),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42)
        }
        
        print(f"Initialized {len(self.models)} models for training")
    
    def train_models(self):
        """
        Train all initialized models and store results.
        """
        print("\nTraining models...")
        print("=" * 50)
        
        for name, model in self.models.items():
            print(f"Training {name}...")
            
            try:
                # Train the model
                model.fit(self.X_train_balanced, self.y_train_balanced)
                
                # Make predictions
                y_pred = model.predict(self.X_test_scaled)
                y_pred_proba = model.predict_proba(self.X_test_scaled)[:, 1] if hasattr(model, 'predict_proba') else None
                
                # Calculate metrics
                accuracy = accuracy_score(self.y_test, y_pred)
                precision = precision_score(self.y_test, y_pred)
                recall = recall_score(self.y_test, y_pred)
                f1 = f1_score(self.y_test, y_pred)
                
                if y_pred_proba is not None:
                    auc = roc_auc_score(self.y_test, y_pred_proba)
                else:
                    auc = None
                
                # Store results
                self.model_results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'auc': auc,
                    'predictions': y_pred,
                    'probabilities': y_pred_proba
                }
                
                print(f"   Accuracy: {accuracy:.4f}")
                print(f"   F1-Score: {f1:.4f}")
                if auc:
                    print(f"   AUC: {auc:.4f}")
                print()
                
            except Exception as e:
                print(f"   Error training {name}: {e}")
                print()
    
    def evaluate_models(self):
        """
        Comprehensive evaluation of all trained models.
        """
        print("\nMODEL EVALUATION RESULTS")
        print("=" * 60)
        
        # Create results DataFrame
        results_data = []
        for name, results in self.model_results.items():
            results_data.append({
                'Model': name,
                'Accuracy': results['accuracy'],
                'Precision': results['precision'],
                'Recall': results['recall'],
                'F1-Score': results['f1_score'],
                'AUC': results['auc'] if results['auc'] else 'N/A'
            })
        
        results_df = pd.DataFrame(results_data)
        results_df = results_df.sort_values('F1-Score', ascending=False)
        
        print(results_df.to_string(index=False))
        
        # Find best model
        best_model_name = results_df.iloc[0]['Model']
        print(f"\nBest performing model: {best_model_name}")
        
        return results_df
    
    def plot_model_comparison(self):
        """
        Create visualizations comparing model performance.
        """
        # Prepare data for plotting
        models = list(self.model_results.keys())
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Model Performance Comparison', fontsize=16)
        
        for i, metric in enumerate(metrics):
            ax = axes[i//2, i%2]
            values = [self.model_results[model][metric] for model in models]
            
            bars = ax.bar(models, values, alpha=0.7)
            ax.set_title(f'{metric.replace("_", " ").title()}')
            ax.set_ylabel('Score')
            ax.set_ylim(0, 1)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom')
            
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_confusion_matrices(self):
        """
        Plot confusion matrices for all models.
        """
        n_models = len(self.model_results)
        cols = 3
        rows = (n_models + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, (name, results) in enumerate(self.model_results.items()):
            row, col = i // cols, i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            cm = confusion_matrix(self.y_test, results['predictions'])
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax)
            ax.set_title(f'{name}')
            ax.set_xlabel('Predicted')
            ax.set_ylabel('Actual')
        
        # Hide empty subplots
        for i in range(n_models, rows * cols):
            row, col = i // cols, i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            ax.set_visible(False)
        
        plt.tight_layout()
        plt.savefig('confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_roc_curves(self):
        """
        Plot ROC curves for models that support probability prediction.
        """
        plt.figure(figsize=(10, 8))
        
        for name, results in self.model_results.items():
            if results['probabilities'] is not None:
                fpr, tpr, _ = roc_curve(self.y_test, results['probabilities'])
                auc = results['auc']
                plt.plot(fpr, tpr, label=f'{name} (AUC = {auc:.3f})')
        
        plt.plot([0, 1], [0, 1], 'k--', label='Random Classifier')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves Comparison')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('roc_curves.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def feature_importance_analysis(self):
        """
        Analyze feature importance for tree-based models.
        """
        tree_models = ['Random Forest', 'XGBoost', 'Gradient Boosting']
        
        fig, axes = plt.subplots(1, len(tree_models), figsize=(20, 6))
        if len(tree_models) == 1:
            axes = [axes]
        
        for i, model_name in enumerate(tree_models):
            if model_name in self.model_results:
                model = self.model_results[model_name]['model']
                
                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                    feature_names = self.X.columns
                    
                    # Sort features by importance
                    indices = np.argsort(importances)[::-1][:10]  # Top 10 features
                    
                    axes[i].bar(range(len(indices)), importances[indices])
                    axes[i].set_title(f'{model_name} - Feature Importance')
                    axes[i].set_xticks(range(len(indices)))
                    axes[i].set_xticklabels([feature_names[j] for j in indices], rotation=45, ha='right')
        
        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def cross_validation_analysis(self):
        """
        Perform cross-validation analysis for model stability assessment.
        """
        print("\nCROSS-VALIDATION ANALYSIS")
        print("=" * 40)
        
        cv_results = {}
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        for name, model in self.models.items():
            try:
                scores = cross_val_score(model, self.X_train_balanced, self.y_train_balanced, 
                                       cv=cv, scoring='f1')
                cv_results[name] = {
                    'mean': scores.mean(),
                    'std': scores.std(),
                    'scores': scores
                }
                print(f"{name}: {scores.mean():.4f} (+/- {scores.std() * 2:.4f})")
            except Exception as e:
                print(f"{name}: Error - {e}")
        
        return cv_results
    
    def save_best_model(self, filename='best_loan_prediction_model.pkl'):
        """
        Save the best performing model.
        
        Args:
            filename (str): Filename to save the model
        """
        if not self.model_results:
            print("No trained models to save!")
            return
        
        # Find best model based on F1-score
        best_model_name = max(self.model_results.keys(), 
                             key=lambda x: self.model_results[x]['f1_score'])
        best_model = self.model_results[best_model_name]['model']
        
        # Save model and scaler
        model_package = {
            'model': best_model,
            'scaler': self.scaler,
            'feature_names': list(self.X.columns),
            'model_name': best_model_name,
            'performance_metrics': self.model_results[best_model_name]
        }
        
        joblib.dump(model_package, filename)
        print(f"Best model ({best_model_name}) saved to {filename}")
        
        return best_model_name, best_model

if __name__ == "__main__":
    # Example usage
    from data_preprocessing import LoanDataPreprocessor
    
    # Load and preprocess data
    preprocessor = LoanDataPreprocessor("ICT701 Assignment3_Question_Dataset.xlsx")
    preprocessor.load_data()
    preprocessor.handle_missing_values()
    preprocessor.feature_engineering()
    preprocessor.encode_categorical_variables()
    
    # Get processed data
    processed_data = preprocessor.get_processed_data()
    
    # Initialize and run predictive analytics
    ml_models = LoanPredictionModels(processed_data)
    ml_models.prepare_features()
    ml_models.split_data()
    ml_models.handle_class_imbalance()
    ml_models.initialize_models()
    ml_models.train_models()
    
    # Evaluate and visualize results
    results_df = ml_models.evaluate_models()
    ml_models.plot_model_comparison()
    ml_models.plot_confusion_matrices()
    ml_models.plot_roc_curves()
    ml_models.feature_importance_analysis()
    ml_models.cross_validation_analysis()
    
    # Save best model
    ml_models.save_best_model()
