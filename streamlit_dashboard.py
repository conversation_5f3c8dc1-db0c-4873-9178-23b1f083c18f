"""
Loan Prediction Business Intelligence Dashboard
==============================================

Interactive Streamlit dashboard for loan prediction analytics,
providing real-time predictions, risk analysis, and business insights.

Author: Business Intelligence Team
Date: 2024
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import joblib
import warnings
warnings.filterwarnings('ignore')

# Import custom modules
try:
    from data_preprocessing import LoanDataPreprocessor
    from predictive_analytics import LoanPredictionModels
    from prescriptive_analytics import LoanPrescriptiveAnalytics
except ImportError:
    st.error("Please ensure all required modules are in the same directory")

# Page configuration
st.set_page_config(
    page_title="Loan Prediction BI Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 5px solid #1f77b4;
    }
    .risk-high {
        color: #d62728;
        font-weight: bold;
    }
    .risk-medium {
        color: #ff7f0e;
        font-weight: bold;
    }
    .risk-low {
        color: #2ca02c;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False
if 'model_trained' not in st.session_state:
    st.session_state.model_trained = False

@st.cache_data
def load_and_process_data():
    """Load and process the loan dataset."""
    try:
        preprocessor = LoanDataPreprocessor("ICT701 Assignment3_Question_Dataset.xlsx")
        data = preprocessor.load_data()
        if data is not None:
            preprocessor.handle_missing_values()
            preprocessor.feature_engineering()
            preprocessor.encode_categorical_variables()
            processed_data = preprocessor.get_processed_data()
            return data, processed_data, preprocessor
        return None, None, None
    except Exception as e:
        st.error(f"Error loading data: {e}")
        return None, None, None

@st.cache_resource
def train_models(processed_data):
    """Train machine learning models."""
    try:
        ml_models = LoanPredictionModels(processed_data)
        ml_models.prepare_features()
        ml_models.split_data()
        ml_models.handle_class_imbalance()
        ml_models.initialize_models()
        ml_models.train_models()
        best_model_name, best_model = ml_models.save_best_model()
        return ml_models, best_model_name
    except Exception as e:
        st.error(f"Error training models: {e}")
        return None, None

def main():
    """Main dashboard function."""
    
    # Header
    st.markdown('<h1 class="main-header">🏦 Loan Prediction Business Intelligence Dashboard</h1>', 
                unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Data Analysis", "Predictive Models", "Risk Assessment", "Loan Prediction", "What-If Analysis"]
    )
    
    # Load data
    if not st.session_state.data_loaded:
        with st.spinner("Loading and processing data..."):
            raw_data, processed_data, preprocessor = load_and_process_data()
            if processed_data is not None:
                st.session_state.raw_data = raw_data
                st.session_state.processed_data = processed_data
                st.session_state.preprocessor = preprocessor
                st.session_state.data_loaded = True
            else:
                st.error("Failed to load data. Please check the dataset file.")
                return
    
    # Train models if not already done
    if st.session_state.data_loaded and not st.session_state.model_trained:
        with st.spinner("Training machine learning models..."):
            ml_models, best_model_name = train_models(st.session_state.processed_data)
            if ml_models is not None:
                st.session_state.ml_models = ml_models
                st.session_state.best_model_name = best_model_name
                st.session_state.model_trained = True
    
    # Page routing
    if page == "Overview":
        show_overview()
    elif page == "Data Analysis":
        show_data_analysis()
    elif page == "Predictive Models":
        show_predictive_models()
    elif page == "Risk Assessment":
        show_risk_assessment()
    elif page == "Loan Prediction":
        show_loan_prediction()
    elif page == "What-If Analysis":
        show_what_if_analysis()

def show_overview():
    """Display overview dashboard."""
    st.header("📊 Executive Dashboard")
    
    if not st.session_state.data_loaded:
        st.warning("Data not loaded yet.")
        return
    
    data = st.session_state.raw_data
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Applications", len(data))
    
    with col2:
        if 'Loan_Status' in data.columns:
            approval_rate = (data['Loan_Status'] == 'Y').mean() * 100
            st.metric("Approval Rate", f"{approval_rate:.1f}%")
    
    with col3:
        if 'LoanAmount' in data.columns:
            avg_loan = data['LoanAmount'].mean()
            st.metric("Avg Loan Amount", f"${avg_loan:,.0f}")
    
    with col4:
        if 'ApplicantIncome' in data.columns:
            avg_income = data['ApplicantIncome'].mean()
            st.metric("Avg Income", f"${avg_income:,.0f}")
    
    # Charts
    col1, col2 = st.columns(2)
    
    with col1:
        if 'Loan_Status' in data.columns:
            fig = px.pie(data, names='Loan_Status', title="Loan Status Distribution")
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        if 'Property_Area' in data.columns:
            fig = px.bar(data['Property_Area'].value_counts(), title="Applications by Property Area")
            st.plotly_chart(fig, use_container_width=True)

def show_data_analysis():
    """Display data analysis page."""
    st.header("📈 Data Analysis")
    
    if not st.session_state.data_loaded:
        st.warning("Data not loaded yet.")
        return
    
    data = st.session_state.raw_data
    
    # Data overview
    st.subheader("Dataset Overview")
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Dataset Shape:**", data.shape)
        st.write("**Missing Values:**")
        missing_data = data.isnull().sum()
        st.write(missing_data[missing_data > 0])
    
    with col2:
        st.write("**Data Types:**")
        st.write(data.dtypes)
    
    # Statistical summary
    st.subheader("Statistical Summary")
    st.write(data.describe())
    
    # Correlation analysis
    st.subheader("Correlation Analysis")
    numeric_cols = data.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 1:
        corr_matrix = data[numeric_cols].corr()
        fig = px.imshow(corr_matrix, text_auto=True, aspect="auto", title="Correlation Matrix")
        st.plotly_chart(fig, use_container_width=True)

def show_predictive_models():
    """Display predictive models page."""
    st.header("🤖 Predictive Models")
    
    if not st.session_state.model_trained:
        st.warning("Models not trained yet.")
        return
    
    ml_models = st.session_state.ml_models
    
    # Model performance comparison
    st.subheader("Model Performance Comparison")
    
    # Create performance DataFrame
    performance_data = []
    for name, results in ml_models.model_results.items():
        performance_data.append({
            'Model': name,
            'Accuracy': results['accuracy'],
            'Precision': results['precision'],
            'Recall': results['recall'],
            'F1-Score': results['f1_score'],
            'AUC': results['auc'] if results['auc'] else 0
        })
    
    performance_df = pd.DataFrame(performance_data)
    st.dataframe(performance_df, use_container_width=True)
    
    # Best model highlight
    best_model = st.session_state.best_model_name
    st.success(f"🏆 Best performing model: **{best_model}**")
    
    # Performance visualization
    fig = px.bar(performance_df, x='Model', y='F1-Score', title="Model F1-Score Comparison")
    st.plotly_chart(fig, use_container_width=True)

def show_risk_assessment():
    """Display risk assessment page."""
    st.header("⚠️ Risk Assessment")
    
    if not st.session_state.model_trained:
        st.warning("Models not trained yet.")
        return
    
    # Initialize prescriptive analytics
    try:
        prescriptive = LoanPrescriptiveAnalytics(
            model_path='best_loan_prediction_model.pkl',
            data=st.session_state.processed_data
        )
        
        # Calculate risk scores
        risk_scores = prescriptive.calculate_risk_scores()
        
        # Risk distribution
        st.subheader("Risk Distribution")
        col1, col2 = st.columns(2)
        
        with col1:
            risk_dist = risk_scores['Risk_Category'].value_counts()
            fig = px.pie(values=risk_dist.values, names=risk_dist.index, 
                        title="Risk Category Distribution")
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            fig = px.histogram(risk_scores, x='Risk_Score', bins=20, 
                             title="Risk Score Distribution")
            st.plotly_chart(fig, use_container_width=True)
        
        # Risk metrics
        st.subheader("Risk Metrics")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            high_risk_count = len(risk_scores[risk_scores['Risk_Category'].isin(['High Risk', 'Very High Risk'])])
            st.metric("High Risk Applications", high_risk_count)
        
        with col2:
            avg_risk = risk_scores['Risk_Score'].mean()
            st.metric("Average Risk Score", f"{avg_risk:.3f}")
        
        with col3:
            low_risk_count = len(risk_scores[risk_scores['Risk_Category'] == 'Low Risk'])
            st.metric("Low Risk Applications", low_risk_count)
        
        with col4:
            approval_rate = risk_scores['Approval_Probability'].mean()
            st.metric("Avg Approval Probability", f"{approval_rate:.1%}")
        
    except Exception as e:
        st.error(f"Error in risk assessment: {e}")

def show_loan_prediction():
    """Display loan prediction interface."""
    st.header("🔮 Loan Prediction")
    
    if not st.session_state.model_trained:
        st.warning("Models not trained yet.")
        return
    
    st.subheader("Enter Applicant Details")
    
    # Input form
    col1, col2 = st.columns(2)
    
    with col1:
        gender = st.selectbox("Gender", ["Male", "Female"])
        married = st.selectbox("Married", ["Yes", "No"])
        dependents = st.selectbox("Dependents", ["0", "1", "2", "3+"])
        education = st.selectbox("Education", ["Graduate", "Not Graduate"])
        self_employed = st.selectbox("Self Employed", ["Yes", "No"])
        
    with col2:
        applicant_income = st.number_input("Applicant Income ($)", min_value=0, value=5000)
        coapplicant_income = st.number_input("Coapplicant Income ($)", min_value=0, value=0)
        loan_amount = st.number_input("Loan Amount ($)", min_value=0, value=100000)
        loan_term = st.selectbox("Loan Amount Term (months)", [120, 180, 240, 300, 360, 480])
        credit_history = st.selectbox("Credit History", ["Good", "Poor"])
        property_area = st.selectbox("Property Area", ["Urban", "Semiurban", "Rural"])
    
    if st.button("Predict Loan Approval", type="primary"):
        try:
            # Load model
            model_package = joblib.load('best_loan_prediction_model.pkl')
            
            # Create input data
            input_data = pd.DataFrame({
                'Gender': [gender],
                'Married': [married],
                'Dependents': [dependents],
                'Education': [education],
                'Self_Employed': [self_employed],
                'ApplicantIncome': [applicant_income],
                'CoapplicantIncome': [coapplicant_income],
                'LoanAmount': [loan_amount],
                'Loan_Amount_Term': [loan_term],
                'Credit_History': [1 if credit_history == "Good" else 0],
                'Property_Area': [property_area]
            })
            
            # Process input data (simplified)
            # Note: In a real implementation, you'd use the same preprocessing pipeline
            
            st.success("Prediction completed!")
            st.info("Note: This is a simplified prediction interface. Full implementation would require complete preprocessing pipeline.")
            
        except Exception as e:
            st.error(f"Error making prediction: {e}")

def show_what_if_analysis():
    """Display what-if analysis page."""
    st.header("🔄 What-If Analysis")
    
    st.subheader("Scenario Analysis")
    st.write("Analyze how changes in applicant characteristics affect loan approval probability.")
    
    # Scenario selection
    scenario_type = st.selectbox(
        "Select Scenario Type",
        ["Income Change", "Credit History Impact", "Loan Amount Variation", "Custom Scenario"]
    )
    
    if scenario_type == "Income Change":
        st.subheader("Income Impact Analysis")
        income_range = st.slider("Income Range ($)", 1000, 20000, (3000, 15000), step=1000)
        
        # Create income scenarios
        incomes = list(range(income_range[0], income_range[1] + 1000, 1000))
        
        st.write("This analysis would show how approval probability changes with income levels.")
        
        # Placeholder chart
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=incomes, y=[0.3 + 0.05*i for i in range(len(incomes))], 
                                mode='lines+markers', name='Approval Probability'))
        fig.update_layout(title="Approval Probability vs Income", 
                         xaxis_title="Income ($)", yaxis_title="Approval Probability")
        st.plotly_chart(fig, use_container_width=True)
    
    elif scenario_type == "Custom Scenario":
        st.subheader("Custom Scenario Builder")
        st.write("Build your own scenario by adjusting multiple parameters.")
        
        # Custom scenario inputs
        col1, col2 = st.columns(2)
        with col1:
            st.number_input("Base Income", value=5000)
            st.selectbox("Base Credit History", ["Good", "Poor"])
        with col2:
            st.number_input("Base Loan Amount", value=100000)
            st.selectbox("Base Property Area", ["Urban", "Rural"])
        
        if st.button("Run Scenario Analysis"):
            st.info("Scenario analysis would be performed here with the selected parameters.")

if __name__ == "__main__":
    main()
