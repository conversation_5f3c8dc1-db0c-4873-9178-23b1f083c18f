# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.tree import DecisionTreeClassifier, export_text
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, 
                           roc_auc_score, confusion_matrix, classification_report, roc_curve)
from imblearn.over_sampling import SMOTE

# Visualization libraries
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo
pyo.init_notebook_mode(connected=True)

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("✅ All libraries imported successfully!")
print("📊 Ready to begin loan prediction analysis...")

# Load the dataset
try:
    data = pd.read_excel('ICT701 Assignment3_Question_Dataset.xlsx')
    print(f"✅ Dataset loaded successfully!")
    print(f"📊 Dataset shape: {data.shape}")
    print(f"📋 Columns: {list(data.columns)}")
except Exception as e:
    print(f"❌ Error loading dataset: {e}")
    print("Please ensure the Excel file is in the same directory")

# Display basic information about the dataset
print("=" * 60)
print("📈 DATASET OVERVIEW")
print("=" * 60)

# Basic info
print(f"\n🔢 Shape: {data.shape}")
print(f"💾 Memory usage: {data.memory_usage(deep=True).sum() / 1024:.2f} KB")

# Display first few rows
print("\n📋 First 5 rows:")
display(data.head())

# Data types
print("\n🏷️ Data types:")
print(data.dtypes)

# Basic statistics
print("\n📊 Statistical summary:")
display(data.describe())

# Data Quality Assessment
print("=" * 60)
print("🔍 DATA QUALITY ASSESSMENT")
print("=" * 60)

# Missing values analysis
print("\n❓ Missing Values Analysis:")
missing_data = data.isnull().sum()
missing_percent = (missing_data / len(data)) * 100
missing_df = pd.DataFrame({
    'Missing Count': missing_data,
    'Missing Percentage': missing_percent
})
missing_summary = missing_df[missing_df['Missing Count'] > 0]
if len(missing_summary) > 0:
    display(missing_summary)
else:
    print("✅ No missing values found!")

# Duplicate records
duplicates = data.duplicated().sum()
print(f"\n🔄 Duplicate records: {duplicates}")

# Unique values in categorical columns
print("\n📊 Categorical Variables Analysis:")
categorical_cols = data.select_dtypes(include=['object']).columns
for col in categorical_cols:
    print(f"\n   {col}:")
    print(f"   Unique values: {data[col].nunique()}")
    print(f"   Value counts:\n{data[col].value_counts()}")

# 1. Missing Values Analysis
print("=" * 60)
print("📊 DATA DISTRIBUTION ANALYSIS")
print("=" * 60)

print("\n🔍 1. Missing Values Analysis")
plt.figure(figsize=(12, 8))
sns.heatmap(data.isnull(), cbar=True, cmap='viridis', yticklabels=False)
plt.title('Missing Values Heatmap', fontsize=16, fontweight='bold')
plt.xlabel('Features', fontsize=12)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

print("\n✅ Missing values visualization completed!")

# 2. Loan Status Distribution
print("\n📊 2. Loan Status Distribution")
if 'Loan_Status' in data.columns:
    plt.figure(figsize=(10, 6))
    loan_counts = data['Loan_Status'].value_counts()
    colors = ['lightgreen', 'lightcoral']
    bars = plt.bar(loan_counts.index, loan_counts.values, color=colors, alpha=0.8, edgecolor='black')
    plt.title('Loan Status Distribution', fontsize=16, fontweight='bold')
    plt.xlabel('Loan Status', fontsize=12)
    plt.ylabel('Number of Applications', fontsize=12)
    
    # Add value labels on bars
    for bar, value in zip(bars, loan_counts.values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5,
                str(value), ha='center', va='bottom', fontweight='bold', fontsize=12)
    
    # Add percentage labels
    total = loan_counts.sum()
    for i, (status, count) in enumerate(loan_counts.items()):
        percentage = (count/total)*100
        plt.text(i, count/2, f'{percentage:.1f}%', ha='center', va='center', 
                fontsize=14, fontweight='bold', color='white')
    
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    print(f"   Approved (Y): {loan_counts.get('Y', 0)} applications")
    print(f"   Rejected (N): {loan_counts.get('N', 0)} applications")
    print(f"   Approval Rate: {(loan_counts.get('Y', 0)/total)*100:.1f}%")
else:
    print("   ⚠️ Loan_Status column not found in dataset")

print("\n✅ Loan status analysis completed!")

# 3. Income Distribution Analysis
print("\n💰 3. Income Distribution Analysis")

# Check for income columns with different possible names
applicant_income_col = None
coapplicant_income_col = None

for col in data.columns:
    if 'applicant' in col.lower() and 'income' in col.lower() and 'coapplicant' not in col.lower():
        applicant_income_col = col
    elif 'coapplicant' in col.lower() and 'income' in col.lower():
        coapplicant_income_col = col

if applicant_income_col:
    fig, axes = plt.subplots(1, 2, figsize=(16, 6))
    
    # Applicant Income Distribution
    axes[0].hist(data[applicant_income_col].dropna(), bins=30, alpha=0.7, 
                color='lightblue', edgecolor='black')
    axes[0].set_title('Applicant Income Distribution', fontweight='bold', fontsize=14)
    axes[0].set_xlabel('Income ($)', fontsize=12)
    axes[0].set_ylabel('Frequency', fontsize=12)
    axes[0].grid(alpha=0.3)
    
    # Add statistics
    mean_income = data[applicant_income_col].mean()
    median_income = data[applicant_income_col].median()
    axes[0].axvline(mean_income, color='red', linestyle='--', 
                   label=f'Mean: ${mean_income:.0f}', linewidth=2)
    axes[0].axvline(median_income, color='blue', linestyle='--', 
                   label=f'Median: ${median_income:.0f}', linewidth=2)
    axes[0].legend()
    
    # Coapplicant Income Distribution (if exists)
    if coapplicant_income_col:
        axes[1].hist(data[coapplicant_income_col].dropna(), bins=30, alpha=0.7, 
                    color='lightgreen', edgecolor='black')
        axes[1].set_title('Coapplicant Income Distribution', fontweight='bold', fontsize=14)
        axes[1].set_xlabel('Income ($)', fontsize=12)
        axes[1].set_ylabel('Frequency', fontsize=12)
        axes[1].grid(alpha=0.3)
        
        # Add statistics
        mean_co_income = data[coapplicant_income_col].mean()
        median_co_income = data[coapplicant_income_col].median()
        axes[1].axvline(mean_co_income, color='red', linestyle='--', 
                       label=f'Mean: ${mean_co_income:.0f}', linewidth=2)
        axes[1].axvline(median_co_income, color='blue', linestyle='--', 
                       label=f'Median: ${median_co_income:.0f}', linewidth=2)
        axes[1].legend()
    else:
        axes[1].text(0.5, 0.5, 'Coapplicant Income\nData Not Available', 
                    ha='center', va='center', transform=axes[1].transAxes, 
                    fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
        axes[1].set_title('Coapplicant Income Distribution', fontweight='bold', fontsize=14)
    
    plt.tight_layout()
    plt.show()
    
    print(f"   Applicant Income - Mean: ${mean_income:.0f}, Median: ${median_income:.0f}")
    if coapplicant_income_col:
        print(f"   Coapplicant Income - Mean: ${mean_co_income:.0f}, Median: ${median_co_income:.0f}")
else:
    print("   ⚠️ Income columns not found in dataset")

print("\n✅ Income analysis completed!")

# 4. Loan Amount Analysis
print("\n🏦 4. Loan Amount Analysis")

# Find loan amount column
loan_amount_col = None
for col in data.columns:
    if 'loan' in col.lower() and 'amount' in col.lower():
        loan_amount_col = col
        break

if loan_amount_col:
    plt.figure(figsize=(12, 6))
    plt.hist(data[loan_amount_col].dropna(), bins=30, alpha=0.7, 
             color='orange', edgecolor='black')
    plt.title('Loan Amount Distribution', fontsize=16, fontweight='bold')
    plt.xlabel('Loan Amount ($)', fontsize=12)
    plt.ylabel('Frequency', fontsize=12)
    plt.grid(alpha=0.3)
    
    # Add statistics
    mean_loan = data[loan_amount_col].mean()
    median_loan = data[loan_amount_col].median()
    plt.axvline(mean_loan, color='red', linestyle='--', 
               label=f'Mean: ${mean_loan:.0f}', linewidth=2)
    plt.axvline(median_loan, color='blue', linestyle='--', 
               label=f'Median: ${median_loan:.0f}', linewidth=2)
    plt.legend()
    plt.tight_layout()
    plt.show()
    
    print(f"   Loan Amount - Mean: ${mean_loan:.0f}, Median: ${median_loan:.0f}")
    print(f"   Loan Amount - Min: ${data[loan_amount_col].min():.0f}, Max: ${data[loan_amount_col].max():.0f}")
else:
    print("   ⚠️ Loan Amount column not found in dataset")

print("\n✅ Loan amount analysis completed!")

# 5. Categorical Variables Analysis
print("\n📋 5. Categorical Variables Analysis")

# Define categorical columns to analyze
categorical_columns = ['Gender', 'Married', 'Education', 'Self_Employed', 'Property_Area']
available_cats = []

# Check which categorical columns exist in the dataset
for col in categorical_columns:
    if col in data.columns:
        available_cats.append(col)
    else:
        # Check for variations in column names
        for data_col in data.columns:
            if col.lower().replace('_', ' ') in data_col.lower():
                available_cats.append(data_col)
                break

if available_cats:
    # Create individual plots for each categorical variable
    for i, col in enumerate(available_cats[:4]):  # Limit to 4 for better display
        plt.figure(figsize=(10, 6))
        
        value_counts = data[col].value_counts()
        colors = plt.cm.Set3(np.linspace(0, 1, len(value_counts)))
        bars = plt.bar(value_counts.index, value_counts.values, 
                      color=colors, alpha=0.8, edgecolor='black')
        
        plt.title(f'{col} Distribution', fontweight='bold', fontsize=16)
        plt.xlabel(col, fontsize=12)
        plt.ylabel('Count', fontsize=12)
        
        # Add value labels on bars
        for bar, value in zip(bars, value_counts.values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                   str(value), ha='center', va='bottom', fontweight='bold')
        
        # Add percentage labels
        total = value_counts.sum()
        for j, (category, count) in enumerate(value_counts.items()):
            percentage = (count/total)*100
            plt.text(j, count/2, f'{percentage:.1f}%', ha='center', va='center', 
                    fontsize=12, fontweight='bold', color='white')
        
        plt.xticks(rotation=45 if len(str(value_counts.index[0])) > 5 else 0)
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        print(f"   {col}: {len(value_counts)} unique values")
        for category, count in value_counts.items():
            print(f"     {category}: {count} ({(count/total)*100:.1f}%)")
        print()
else:
    print("   ⚠️ No categorical columns found for analysis")

print("\n✅ Categorical variables analysis completed!")

# 6. Feature Relationships with Loan Status
print("\n🔗 6. Feature Relationships with Loan Status")

if 'Loan_Status' in data.columns:
    # Credit History vs Loan Status
    credit_col = None
    for col in data.columns:
        if 'credit' in col.lower() and 'history' in col.lower():
            credit_col = col
            break
    
    if credit_col:
        plt.figure(figsize=(15, 6))
        
        # Credit History vs Loan Status
        plt.subplot(1, 2, 1)
        credit_loan_crosstab = pd.crosstab(data[credit_col], data['Loan_Status'])
        credit_loan_crosstab.plot(kind='bar', ax=plt.gca(), 
                                 color=['lightcoral', 'lightgreen'], alpha=0.8)
        plt.title('Credit History vs Loan Status', fontweight='bold', fontsize=14)
        plt.xlabel('Credit History (0=Poor, 1=Good)', fontsize=12)
        plt.ylabel('Count', fontsize=12)
        plt.xticks(rotation=0)
        plt.legend(title='Loan Status', title_fontsize=12)
        plt.grid(axis='y', alpha=0.3)
        
        # Education vs Loan Status
        education_col = None
        for col in data.columns:
            if 'education' in col.lower():
                education_col = col
                break
        
        if education_col:
            plt.subplot(1, 2, 2)
            edu_loan_crosstab = pd.crosstab(data[education_col], data['Loan_Status'])
            edu_loan_crosstab.plot(kind='bar', ax=plt.gca(), 
                                  color=['lightcoral', 'lightgreen'], alpha=0.8)
            plt.title('Education vs Loan Status', fontweight='bold', fontsize=14)
            plt.xlabel('Education Level', fontsize=12)
            plt.ylabel('Count', fontsize=12)
            plt.xticks(rotation=45)
            plt.legend(title='Loan Status', title_fontsize=12)
            plt.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # Print insights
        if credit_col:
            approval_rate_good_credit = credit_loan_crosstab.loc[1.0, 'Y'] / credit_loan_crosstab.loc[1.0].sum() * 100
            approval_rate_poor_credit = credit_loan_crosstab.loc[0.0, 'Y'] / credit_loan_crosstab.loc[0.0].sum() * 100
            print(f"   Good Credit History Approval Rate: {approval_rate_good_credit:.1f}%")
            print(f"   Poor Credit History Approval Rate: {approval_rate_poor_credit:.1f}%")
    else:
        print("   ⚠️ Credit History column not found")
else:
    print("   ⚠️ Loan_Status column not found for relationship analysis")

print("\n✅ Feature relationships analysis completed!")

# 7. Correlation Analysis
print("\n🔗 7. Correlation Analysis")
numeric_cols = data.select_dtypes(include=[np.number]).columns
if len(numeric_cols) > 1:
    plt.figure(figsize=(14, 10))
    correlation_matrix = data[numeric_cols].corr()
    
    # Create heatmap with triangular mask
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')
    plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()
    
    # Print strong correlations
    print("\n   Strong correlations (>0.5 or <-0.5):")
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_val = correlation_matrix.iloc[i, j]
            if abs(corr_val) > 0.5:
                print(f"     {correlation_matrix.columns[i]} vs {correlation_matrix.columns[j]}: {corr_val:.3f}")
else:
    print("   ⚠️ Not enough numeric columns for correlation analysis")

print("\n✅ Correlation analysis completed!")
print("\n" + "=" * 60)
print("🎉 DATA VISUALIZATION ANALYSIS COMPLETED!")
print("=" * 60)
print("\n📊 All visualizations are now properly sized and informative.")
print("📈 Each chart provides clear insights into the loan prediction dataset.")
print("🔍 Use these visualizations to understand data patterns and relationships.")

# Data Preprocessing Pipeline
print("=" * 60)
print("🔧 DATA PREPROCESSING PIPELINE")
print("=" * 60)

# Create a copy for processing
processed_data = data.copy()
print(f"\n📋 Original dataset shape: {processed_data.shape}")

# Step 1: Handle Missing Values
print("\n🔍 Step 1: Handling Missing Values")
missing_cols = processed_data.columns[processed_data.isnull().any()].tolist()

for col in missing_cols:
    missing_count = processed_data[col].isnull().sum()
    missing_percent = (missing_count / len(processed_data)) * 100
    
    print(f"   Processing {col}: {missing_count} missing values ({missing_percent:.2f}%)")
    
    if missing_percent > 50:
        # Drop columns with >50% missing values
        processed_data.drop(col, axis=1, inplace=True)
        print(f"   -> Dropped column (>50% missing)")
    elif processed_data[col].dtype == 'object':
        # For categorical variables, use mode
        mode_value = processed_data[col].mode()[0] if not processed_data[col].mode().empty else 'Unknown'
        processed_data[col].fillna(mode_value, inplace=True)
        print(f"   -> Filled with mode: {mode_value}")
    else:
        # For numerical variables, use median
        median_value = processed_data[col].median()
        processed_data[col].fillna(median_value, inplace=True)
        print(f"   -> Filled with median: {median_value}")

print(f"\n✅ Missing values after processing: {processed_data.isnull().sum().sum()}")

# Step 2: Feature Engineering
print("\n🛠️ Step 2: Feature Engineering")

# Find income columns with flexible naming
applicant_income_col = None
coapplicant_income_col = None
loan_amount_col = None

for col in processed_data.columns:
    if 'applicant' in col.lower() and 'income' in col.lower() and 'coapplicant' not in col.lower():
        applicant_income_col = col
    elif 'coapplicant' in col.lower() and 'income' in col.lower():
        coapplicant_income_col = col
    elif 'loan' in col.lower() and 'amount' in col.lower() and 'term' not in col.lower():
        loan_amount_col = col

# Create total income feature
if applicant_income_col and coapplicant_income_col:
    processed_data['TotalIncome'] = processed_data[applicant_income_col] + processed_data[coapplicant_income_col]
    print("   ✅ Created TotalIncome feature")

# Create loan amount to income ratio
if loan_amount_col and 'TotalIncome' in processed_data.columns:
    processed_data['LoanAmountToIncomeRatio'] = processed_data[loan_amount_col] / (processed_data['TotalIncome'] + 1)
    print("   ✅ Created LoanAmountToIncomeRatio feature")

# Create income per dependent
if 'TotalIncome' in processed_data.columns and 'Dependents' in processed_data.columns:
    # Convert Dependents to numeric (handle '3+' case)
    processed_data['Dependents_Numeric'] = processed_data['Dependents'].replace('3+', '3').astype(float)
    processed_data['IncomePerDependent'] = processed_data['TotalIncome'] / (processed_data['Dependents_Numeric'] + 1)
    print("   ✅ Created IncomePerDependent feature")

# Create loan term categories
loan_term_col = None
for col in processed_data.columns:
    if 'loan' in col.lower() and 'term' in col.lower():
        loan_term_col = col
        break

if loan_term_col:
    processed_data['LoanTermCategory'] = pd.cut(
        processed_data[loan_term_col], 
        bins=[0, 120, 240, 360, 480], 
        labels=['Short', 'Medium', 'Long', 'Very Long']
    )
    print("   ✅ Created LoanTermCategory feature")

# Create income categories
if 'TotalIncome' in processed_data.columns:
    processed_data['IncomeCategory'] = pd.cut(
        processed_data['TotalIncome'],
        bins=[0, 3000, 6000, 10000, float('inf')],
        labels=['Low', 'Medium', 'High', 'Very High']
    )
    print("   ✅ Created IncomeCategory feature")

print(f"\n📊 Dataset shape after feature engineering: {processed_data.shape}")

